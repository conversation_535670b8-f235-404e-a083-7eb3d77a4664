<?php
// Handle contact form submission
$message = '';
$messageType = '';

if ($_POST && isset($_POST['form_type']) && $_POST['form_type'] === 'contact') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $company = $_POST['company'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message_text = $_POST['message'] ?? '';
    $date = date('Y-m-d H:i:s');

    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message_text)) {
        $message = 'Por favor completa todos los campos obligatorios.';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Por favor ingresa un email válido.';
        $messageType = 'error';
    } else {
        // Save to CSV
        $csvData = [$date, 'Contacto', $name, $email, $phone, $company, $subject, $message_text];
        $file = fopen('../contacts.csv', 'a');
        fputcsv($file, $csvData, ',', '"', '\\');
        fclose($file);

        $message = '¡Gracias por contactarnos! Te responderemos pronto.';
        $messageType = 'success';
    }
}

// Set page title
$page_title = 'Contacto - APPI | Asociación de Parques Industriales del Paraguay';

// Include header
include '../includes/header.php';
?>



<!-- Hero Section -->
<section id="relume" class="hero-gradient px-4 py-12 sm:px-6 sm:py-16 md:py-20 lg:py-24 xl:py-32">
  <div class="container mx-auto max-w-7xl">
    <div class="flex flex-col items-center text-center">
      <div class="w-full max-w-3xl lg:max-w-4xl">
        <h1 class="mb-6 text-3xl font-bold text-paraguay-blue sm:mb-8 sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl leading-tight">
          <span class="text-paraguay-red">Contáctanos</span> hoy mismo
        </h1>
        <p class="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-700 leading-relaxed max-w-2xl lg:max-w-3xl mx-auto">
          Estamos aquí para ayudarte con tus consultas sobre desarrollo industrial en Paraguay.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Contact Information Section -->
<section id="relume" class="bg-white px-4 py-12 sm:px-6 sm:py-16 md:py-20 lg:py-24 xl:py-32">
  <div class="container mx-auto max-w-7xl">
    <div class="grid auto-cols-fr grid-cols-1 gap-y-8 sm:gap-y-12 md:grid-cols-[0.5fr_1fr] md:gap-x-8 lg:gap-x-12 xl:gap-x-16 md:gap-y-0">
      <div class="grid auto-cols-fr grid-cols-1 gap-x-4 gap-y-12">
        <div class="bg-gray-50 p-6 rounded-2xl shadow-lg card-hover">
          <div class="mb-4 md:mb-6">
            <div class="w-12 h-12 bg-paraguay-red rounded-full flex items-center justify-center">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="w-6 h-6 text-white" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 4H4c-1.103 0-2 .897-2 2v12c0 1.103.897 2 2 2h16c1.103 0 2-.897 2-2V6c0-1.103-.897-2-2-2zm0 2v.511l-8 6.223-8-6.222V6h16zM4 18V9.044l7.386 5.745a.994.994 0 0 0 1.228 0L20 9.044 20.002 18H4z"></path>
              </svg>
            </div>
          </div>
          <h3 class="mb-3 text-lg leading-[1.4] font-bold text-paraguay-blue md:text-xl">Correo Electrónico</h3>
          <p class="mb-3 text-gray-700">Escríbenos a</p>
          <a class="text-paraguay-red hover:text-paraguay-dark-red font-semibold transition-colors" href="mailto:<EMAIL>"><EMAIL></a>
        </div>

        <div class="bg-gray-50 p-6 rounded-2xl shadow-lg card-hover">
          <div class="mb-4 md:mb-6">
            <div class="w-12 h-12 bg-paraguay-blue rounded-full flex items-center justify-center">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="w-6 h-6 text-white" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M17.707 12.293a.999.999 0 0 0-1.414 0l-1.594 1.594c-.739-.22-2.118-.72-2.992-1.594s-1.374-2.253-1.594-2.992l1.594-1.594a.999.999 0 0 0 0-1.414l-4-4a.999.999 0 0 0-1.414 0L3.581 5.005c-.38.38-.594.902-.586 1.435.023 1.424.4 6.37 4.298 10.268s8.844 4.274 10.269 4.298h.028c.528 0 1.027-.208 1.405-.586l2.712-2.712a.999.999 0 0 0 0-1.414l-4-4.001zm-.127 6.712c-1.248-.021-5.518-.356-8.873-3.712-3.366-3.366-3.692-7.651-3.712-8.874L7 4.414 9.586 7 8.293 8.293a1 1 0 0 0-.272.912c.024.115.611 2.842 2.271 4.502s4.387 2.247 4.502 2.271a.991.991 0 0 0 .912-.271L17 14.414 19.586 17l-2.006 2.005z"></path>
              </svg>
            </div>
          </div>
          <h3 class="mb-3 text-lg leading-[1.4] font-bold text-paraguay-blue md:text-xl">Teléfono</h3>
          <p class="mb-3 text-gray-700">Llámanos al</p>
          <a class="text-paraguay-red hover:text-paraguay-dark-red font-semibold transition-colors" href="tel:+595981229244">+595 981 229 244</a>
        </div>

        <div class="bg-gray-50 p-6 rounded-2xl shadow-lg card-hover">
          <div class="mb-4 md:mb-6">
            <div class="w-12 h-12 bg-paraguay-red rounded-full flex items-center justify-center">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="w-6 h-6 text-white" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 14c2.206 0 4-1.794 4-4s-1.794-4-4-4-4 1.794-4 4 1.794 4 4 4zm0-6c1.103 0 2 .897 2 2s-.897 2-2 2-2-.897-2-2 .897-2 2-2z"></path>
                <path d="M11.42 21.814a.998.998 0 0 0 1.16 0C12.884 21.599 20.029 16.44 20 10c0-4.411-3.589-8-8-8S4 5.589 4 9.995c-.029 6.445 7.116 11.604 7.42 11.819zM12 4c3.309 0 6 2.691 6 6.005.021 4.438-4.388 8.423-6 9.73-1.611-1.308-6.021-5.294-6-9.735 0-3.309 2.691-6 6-6z"></path>
              </svg>
            </div>
          </div>
          <h3 class="mb-3 text-lg leading-[1.4] font-bold text-paraguay-blue md:text-xl">Oficina Principal</h3>
          <p class="mb-3 text-gray-700">Edificio Dot Sacramento - Santísimo Sacramento 2265, Asunción, Paraguay</p>
          <div class="mt-6 md:mt-8">
            <a href="https://maps.google.com/?q=Edificio+Dot+Sacramento,+Santísimo+Sacramento+2265,+Asunción,+Paraguay" target="_blank" class="w-full inline-flex items-center justify-center whitespace-nowrap transition-all duration-300 ease-in-out gap-2 text-paraguay-red hover:text-paraguay-dark-red font-semibold" title="Obtener Direcciones">
              Obtener Direcciones<svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 15 15" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.1584 3.13508C6.35985 2.94621 6.67627 2.95642 6.86514 3.15788L10.6151 7.15788C10.7954 7.3502 10.7954 7.64949 10.6151 7.84182L6.86514 11.8418C6.67627 12.0433 6.35985 12.0535 6.1584 11.8646C5.95694 11.6757 5.94673 11.3593 6.1356 11.1579L9.565 7.49985L6.1356 3.84182C5.94673 3.64036 5.95694 3.32394 6.1584 3.13508Z" fill="currentColor"></path>
              </svg>
            </a>
          </div>
        </div>

        <!-- Additional Contact Info -->
        <div class="bg-gray-50 p-6 rounded-2xl shadow-lg card-hover">
          <div class="mb-4 md:mb-6">
            <div class="w-12 h-12 bg-paraguay-blue rounded-full flex items-center justify-center">
              <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 24 24" class="w-6 h-6 text-white" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10 10-4.486 10-10S17.514 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"></path>
                <path d="M13 7h-2v5.414l3.293 3.293 1.414-1.414L13 11.586z"></path>
              </svg>
            </div>
          </div>
          <h3 class="mb-3 text-lg leading-[1.4] font-bold text-paraguay-blue md:text-xl">Horarios de Atención</h3>
          <div class="space-y-2 text-gray-700">
            <p><strong>Lunes a Viernes:</strong> 8:00 - 17:00</p>
            <p><strong>Sábados:</strong> 8:00 - 12:00</p>
            <p><strong>Domingos:</strong> Cerrado</p>
          </div>
        </div>
      </div>

      <div class="justify-self-end md:w-[321.6px] lg:w-auto">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d6267.400786890681!2d-57.581932900000005!3d-25.263226000000003!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x945da70024d1164f%3A0x24af84ac4ddd864c!2sEdificio%20Dot%20Sacramento!5e1!3m2!1ses-419!2spy!4v1751331525717!5m2!1ses-419!2spy"
          class="size-full h-[400px] md:h-[516px] rounded-2xl shadow-xl card-hover"
          style="border:0;"
          allowfullscreen=""
          loading="lazy"
          referrerpolicy="no-referrer-when-downgrade"
          title="Ubicación de APPI - Edificio Dot Sacramento">
        </iframe>
      </div>
    </div>
  </div>
</section>

<?php if ($message): ?>
<div class="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg <?php echo $messageType === 'success' ? 'bg-green-500' : 'bg-red-500'; ?> text-white">
  <?php echo htmlspecialchars($message); ?>
</div>
<script>
  setTimeout(function() {
    document.querySelector('.fixed').style.display = 'none';
  }, 5000);
</script>
<?php endif; ?>

<!-- Contact Form Section -->
<section id="formulario" class="bg-paraguay-light-blue px-[5%] py-20 md:py-28 lg:py-32">
  <div class="container mx-auto">
    <div class="flex flex-col items-center">
      <div class="mb-16 w-full max-w-4xl text-center md:mb-20 lg:mb-24">
        <p class="mb-4 font-semibold text-paraguay-red text-lg md:mb-6">Formulario de Contacto</p>
        <h2 class="mb-6 text-4xl font-bold text-paraguay-blue md:mb-8 md:text-6xl lg:text-7xl leading-tight">
          Envíanos un <span class="text-paraguay-red">mensaje</span>
        </h2>
        <p class="text-lg text-gray-700 leading-relaxed">
          Completa el formulario y nos pondremos en contacto contigo a la brevedad.
        </p>
      </div>

      <div class="w-full max-w-4xl">
        <form method="POST" id="contactForm" class="bg-white p-8 rounded-2xl shadow-lg">
          <input type="hidden" name="form_type" value="contact">

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Nombre completo *</label>
              <input type="text" id="name" name="name" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200"
                     placeholder="Ingresa tu nombre completo">
              <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200"
                     placeholder="<EMAIL>">
              <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Teléfono</label>
              <input type="tel" id="phone" name="phone"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200"
                     placeholder="+595 XXX XXX XXX">
              <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>
            <div>
              <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Empresa/Organización</label>
              <input type="text" id="company" name="company"
                     class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200"
                     placeholder="Nombre de tu empresa">
              <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
            </div>
          </div>

          <div class="mb-6">
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Asunto *</label>
            <select id="subject" name="subject" required
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200">
              <option value="">Selecciona un asunto</option>
              <option value="informacion_general">Información General</option>
              <option value="asociacion">Solicitud de Asociación</option>
              <option value="servicios">Consulta sobre Servicios</option>
              <option value="eventos">Eventos y Capacitaciones</option>
              <option value="prensa">Consultas de Prensa</option>
              <option value="otro">Otro</option>
            </select>
            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
          </div>

          <div class="mb-6">
            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Mensaje *</label>
            <textarea id="message" name="message" rows="5" required
                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-paraguay-red focus:border-transparent transition-all duration-200"
                      placeholder="Escribe tu mensaje aquí..."></textarea>
            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
            <div class="text-sm text-gray-500 mt-1">
              <span id="charCount">0</span>/500 caracteres
            </div>
          </div>

          <div class="mb-6">
            <label class="flex items-center">
              <input type="checkbox" id="privacy" name="privacy" required
                     class="w-4 h-4 text-paraguay-red bg-gray-100 border-gray-300 rounded focus:ring-paraguay-red focus:ring-2">
              <span class="ml-2 text-sm text-gray-700">
                Acepto la <a href="#" class="text-paraguay-red hover:text-paraguay-dark-red underline">política de privacidad</a> y el tratamiento de mis datos personales *
              </span>
            </label>
            <div class="error-message text-red-500 text-sm mt-1 hidden"></div>
          </div>

          <div class="text-center">
            <button type="submit" id="submitBtn"
                    class="btn-primary rounded-lg inline-flex gap-3 items-center justify-center whitespace-nowrap transition-all duration-300 ease-in-out px-8 py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed">
              <span class="submit-text">Enviar Mensaje</span>
              <span class="loading-text hidden">Enviando...</span>
              <svg class="loading-spinner hidden animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Additional Contact Methods -->
<section class="bg-white px-4 py-12 sm:px-6 sm:py-16 md:py-20">
  <div class="container mx-auto max-w-7xl">
    <div class="text-center mb-12">
      <h2 class="text-3xl font-bold text-paraguay-blue mb-4 md:text-4xl">Otras formas de conectar</h2>
      <p class="text-lg text-gray-700">Síguenos en nuestras redes sociales y mantente al día con las últimas noticias</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- WhatsApp -->
      <div class="bg-green-50 p-6 rounded-2xl text-center card-hover">
        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
          </svg>
        </div>
        <h3 class="font-bold text-gray-900 mb-2">WhatsApp</h3>
        <p class="text-sm text-gray-600 mb-4">Chatea con nosotros</p>
        <a href="https://wa.me/595981229244" target="_blank" class="btn-secondary rounded-lg inline-flex items-center justify-center px-4 py-2 text-sm font-semibold">
          Enviar mensaje
        </a>
      </div>

      <!-- LinkedIn -->
      <div class="bg-blue-50 p-6 rounded-2xl text-center card-hover">
        <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </div>
        <h3 class="font-bold text-gray-900 mb-2">LinkedIn</h3>
        <p class="text-sm text-gray-600 mb-4">Red profesional</p>
        <a href="#" target="_blank" class="btn-secondary rounded-lg inline-flex items-center justify-center px-4 py-2 text-sm font-semibold">
          Seguir
        </a>
      </div>

      <!-- Facebook -->
      <div class="bg-blue-50 p-6 rounded-2xl text-center card-hover">
        <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
        </div>
        <h3 class="font-bold text-gray-900 mb-2">Facebook</h3>
        <p class="text-sm text-gray-600 mb-4">Síguenos</p>
        <a href="#" target="_blank" class="btn-secondary rounded-lg inline-flex items-center justify-center px-4 py-2 text-sm font-semibold">
          Me gusta
        </a>
      </div>

      <!-- Newsletter -->
      <div class="bg-red-50 p-6 rounded-2xl text-center card-hover">
        <div class="w-16 h-16 bg-paraguay-red rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
          </svg>
        </div>
        <h3 class="font-bold text-gray-900 mb-2">Newsletter</h3>
        <p class="text-sm text-gray-600 mb-4">Recibe noticias</p>
        <a href="#formulario" class="btn-primary rounded-lg inline-flex items-center justify-center px-4 py-2 text-sm font-semibold">
          Suscribirse
        </a>
      </div>
    </div>
  </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.getElementById('submitBtn');
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('charCount');

    // Character counter for message
    messageTextarea.addEventListener('input', function() {
        const length = this.value.length;
        charCount.textContent = length;

        if (length > 500) {
            charCount.classList.add('text-red-500');
            charCount.classList.remove('text-gray-500');
        } else {
            charCount.classList.remove('text-red-500');
            charCount.classList.add('text-gray-500');
        }
    });

    // Form validation
    function validateField(field) {
        const errorDiv = field.parentNode.querySelector('.error-message');
        let isValid = true;
        let errorMessage = '';

        // Clear previous error
        errorDiv.classList.add('hidden');
        field.classList.remove('border-red-500');

        // Required field validation
        if (field.hasAttribute('required') && !field.value.trim()) {
            isValid = false;
            errorMessage = 'Este campo es obligatorio';
        }

        // Email validation
        if (field.type === 'email' && field.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(field.value)) {
                isValid = false;
                errorMessage = 'Por favor ingresa un email válido';
            }
        }

        // Phone validation (optional but if provided should be valid)
        if (field.type === 'tel' && field.value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
            if (!phoneRegex.test(field.value)) {
                isValid = false;
                errorMessage = 'Por favor ingresa un teléfono válido';
            }
        }

        // Message length validation
        if (field.id === 'message' && field.value.length > 500) {
            isValid = false;
            errorMessage = 'El mensaje no puede exceder 500 caracteres';
        }

        // Show error if invalid
        if (!isValid) {
            errorDiv.textContent = errorMessage;
            errorDiv.classList.remove('hidden');
            field.classList.add('border-red-500');
        }

        return isValid;
    }

    // Real-time validation
    const fields = form.querySelectorAll('input, textarea, select');
    fields.forEach(field => {
        field.addEventListener('blur', () => validateField(field));
        field.addEventListener('input', () => {
            if (field.classList.contains('border-red-500')) {
                validateField(field);
            }
        });
    });

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all fields
        let isFormValid = true;
        fields.forEach(field => {
            if (!validateField(field)) {
                isFormValid = false;
            }
        });

        // Check privacy checkbox
        const privacyCheckbox = document.getElementById('privacy');
        if (!privacyCheckbox.checked) {
            const errorDiv = privacyCheckbox.parentNode.parentNode.querySelector('.error-message');
            errorDiv.textContent = 'Debes aceptar la política de privacidad';
            errorDiv.classList.remove('hidden');
            isFormValid = false;
        }

        if (isFormValid) {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.querySelector('.submit-text').classList.add('hidden');
            submitBtn.querySelector('.loading-text').classList.remove('hidden');
            submitBtn.querySelector('.loading-spinner').classList.remove('hidden');

            // Submit form
            setTimeout(() => {
                form.submit();
            }, 500);
        } else {
            // Scroll to first error
            const firstError = form.querySelector('.border-red-500');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
