<?php
// Determine base path for footer links
$current_dir = basename(dirname($_SERVER['PHP_SELF']));
$base_path = '';
if ($current_dir !== 'inicio' && $current_dir !== '') {
    $base_path = '../';
}
?>

<footer id="relume" class="navbar-gradient px-4 py-12 sm:px-6 sm:py-16 md:py-20 lg:py-24">
  <div class="container mx-auto max-w-7xl">
    <div
      class="grid grid-cols-1 items-center justify-center justify-items-center gap-y-8 pb-8 sm:gap-y-12 sm:pb-12 md:pb-16 lg:grid-cols-[0.25fr_1fr_0.25fr] lg:justify-between lg:gap-y-4 lg:pb-20"
    >
      <a href="<?php echo $base_path; ?>inicio/index.php" class="lg:justify-self-start">
        <div class="flex items-center space-x-2 sm:space-x-3">
          <div class="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center">
            <span class="text-lg sm:text-xl md:text-2xl font-bold text-paraguay-blue">APPI</span>
          </div>
          <div>
            <h1 class="text-white font-bold text-sm sm:text-base md:text-lg">APPI</h1>
            <p class="text-white text-xs opacity-90">Asociación de Parques Industriales del Paraguay</p>
          </div>
        </div>
      </a>
      <ul
        class="grid grid-flow-row grid-cols-2 items-start justify-center justify-items-center gap-3 sm:gap-4 md:grid-flow-col md:grid-cols-[max-content] md:justify-center md:justify-items-start md:gap-6"
      >
        <li class="font-semibold text-sm sm:text-base">
          <a href="<?php echo $base_path; ?>inicio/index.php" class="text-white hover:text-yellow-200 transition-colors">Inicio</a>
        </li>
        <li class="font-semibold text-sm sm:text-base">
          <a href="<?php echo $base_path; ?>contacto/index.php" class="text-white hover:text-yellow-200 transition-colors">Contacto</a>
        </li>
        <li class="font-semibold text-sm sm:text-base">
          <a href="<?php echo $base_path; ?>cómo-asociarse/index.php" class="text-white hover:text-yellow-200 transition-colors">Asociarse</a>
        </li>
        <li class="font-semibold text-sm sm:text-base">
          <a href="<?php echo $base_path; ?>nosotros/index.php" class="text-white hover:text-yellow-200 transition-colors">Nosotros</a>
        </li>
        <li class="font-semibold text-sm sm:text-base">
          <a href="<?php echo $base_path; ?>servicios/index.php" class="text-white hover:text-yellow-200 transition-colors">Servicios</a>
        </li>
      </ul>
      <div
        class="flex items-start justify-center gap-x-3 sm:gap-x-4 lg:justify-self-end"
      >
        <a href="#" class="w-8 h-8 sm:w-10 sm:h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
          ><svg
            stroke="currentColor"
            fill="currentColor"
            stroke-width="0"
            viewBox="0 0 24 24"
            class="w-5 h-5 text-paraguay-blue"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M12.001 2.002c-5.522 0-9.999 4.477-9.999 9.999 0 4.99 3.656 9.126 8.437 9.879v-6.988h-2.54v-2.891h2.54V9.798c0-2.508 1.493-3.891 3.776-3.891 1.094 0 2.24.195 2.24.195v2.459h-1.264c-1.24 0-1.628.772-1.628 1.563v1.875h2.771l-.443 2.891h-2.328v6.988C18.344 21.129 22 16.992 22 12.001c0-5.522-4.477-9.999-9.999-9.999z"
            ></path></svg></a
        ><a href="#" class="w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
          ><svg
            stroke="currentColor"
            fill="currentColor"
            stroke-width="0"
            viewBox="0 0 24 24"
            class="w-5 h-5 text-paraguay-blue"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M11.999 7.377a4.623 4.623 0 1 0 0 9.248 4.623 4.623 0 0 0 0-9.248zm0 7.627a3.004 3.004 0 1 1 0-6.008 3.004 3.004 0 0 1 0 6.008z"
            ></path>
            <circle cx="16.806" cy="7.207" r="1.078"></circle>
            <path
              d="M20.533 6.111A4.605 4.605 0 0 0 17.9 3.479a6.606 6.606 0 0 0-2.186-.42c-.963-.042-1.268-.054-3.71-.054s-2.755 0-3.71.054a6.554 6.554 0 0 0-2.184.42 4.6 4.6 0 0 0-2.633 2.632 6.585 6.585 0 0 0-.419 2.186c-.043.962-.056 1.267-.056 3.71 0 2.442 0 2.753.056 3.71.015.748.156 1.486.419 2.187a4.61 4.61 0 0 0 2.634 2.632 6.584 6.584 0 0 0 2.185.45c.963.042 1.268.055 3.71.055s2.755 0 3.71-.055a6.615 6.615 0 0 0 2.186-.419 4.613 4.613 0 0 0 2.633-2.633c.263-.7.404-1.438.419-2.186.043-.962.056-1.267.056-3.71s0-2.753-.056-3.71a6.581 6.581 0 0 0-.421-2.217zm-1.218 9.532a5.043 5.043 0 0 1-.311 1.688 2.987 2.987 0 0 1-1.712 1.711 4.985 4.985 0 0 1-1.67.311c-.95.044-1.218.055-3.654.055-2.438 0-2.687 0-3.655-.055a4.96 4.96 0 0 1-1.669-.311 2.985 2.985 0 0 1-1.719-1.711 5.08 5.08 0 0 1-.311-1.669c-.043-.95-.053-1.218-.053-3.654 0-2.437 0-2.686.053-3.655a5.038 5.038 0 0 1 .311-1.687c.305-.789.93-1.41 1.719-1.712a5.01 5.01 0 0 1 1.669-.311c.951-.043 1.218-.055 3.655-.055s2.687 0 3.654.055a4.96 4.96 0 0 1 1.67.311 2.991 2.991 0 0 1 1.712 1.712 5.08 5.08 0 0 1 .311 1.669c.043.951.054 1.218.054 3.655 0 2.436 0 2.698-.043 3.654h-.011z"
            ></path></svg></a
        ><a href="#" class="w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
          ><svg
            stroke="currentColor"
            fill="currentColor"
            stroke-width="0"
            viewBox="0 0 24 24"
            class="w-5 h-5 text-paraguay-blue"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20 3H4a1 1 0 0 0-1 1v16a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zM8.339 18.337H5.667v-8.59h2.672v8.59zM7.003 8.574a1.548 1.548 0 1 1 0-3.096 1.548 1.548 0 0 1 0 3.096zm11.335 9.763h-2.669V14.16c0-.996-.018-2.277-1.388-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248h-2.667v-8.59h2.56v1.174h.037c.355-.675 1.227-1.387 2.524-1.387 2.704 0 3.203 1.778 3.203 4.092v4.71z"
            ></path></svg></a
        ><a href="#" class="w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-colors"
          ><svg
            stroke="currentColor"
            fill="currentColor"
            stroke-width="0"
            viewBox="0 0 24 24"
            class="w-5 h-5 text-paraguay-blue"
            height="1em"
            width="1em"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21.593 7.203a2.506 2.506 0 0 0-1.762-1.766C18.265 5.007 12 5 12 5s-6.264-.007-7.831.404a2.56 2.56 0 0 0-1.766 1.778c-.413 1.566-.417 4.814-.417 4.814s-.004 3.264.406 4.814c.23.857.905 1.534 1.763 1.765 1.582.43 7.83.437 7.83.437s6.265.007 7.831-.403a2.515 2.515 0 0 0 1.767-1.763c.414-1.565.417-4.812.417-4.812s.02-3.265-.407-4.831zM9.996 15.005l.005-6 5.207 3.005-5.212 2.995z"
            ></path></svg
        ></a>
      </div>
    </div>
    <div class="h-px w-full bg-white opacity-30"></div>
    <div
      class="flex flex-col-reverse items-center justify-center justify-items-center pt-6 pb-3 sm:pt-8 sm:pb-4 md:flex-row md:gap-x-6 md:pt-12 md:pb-0"
    >
      <ul
        class="grid grid-flow-row grid-cols-[max-content] items-center justify-center justify-items-center gap-y-3 sm:gap-y-4 md:grid-flow-col md:gap-x-6 md:gap-y-0"
      >
        <p class="mt-6 text-xs sm:text-sm md:mt-0 text-white opacity-90 text-center">© 2024 APPI - Asociación de Parques Industriales del Paraguay. Todos los derechos reservados.</p>
        <div class="mt-4 text-center text-white opacity-90">
          <p class="text-xs sm:text-sm mb-2">
            <strong>Teléfono:</strong> Llámanos al +595 981 229 244
          </p>
          <p class="text-xs sm:text-sm">
            <strong>Oficina Principal:</strong> Edificio Dot Sacramento - Santísimo Sacramento 2265, Asunción, Paraguay
          </p>
        </div>
      </ul>
    </div>
  </div>
</footer>

</body>
</html>
