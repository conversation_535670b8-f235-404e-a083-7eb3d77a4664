<?php
// Get current page for navigation highlighting
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Determine base path for navigation links
$base_path = '';
if ($current_dir !== '' && $current_dir !== '') {
    $base_path = '../';
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'APPI - Asociación de Parques Industriales del Paraguay'; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            --paraguay-red: #D52B1E;
            --paraguay-blue: #0038A8;
            --paraguay-white: #FFFFFF;
            --paraguay-light-blue: #E6F3FF;
            --paraguay-light-red: #FFF5F5;
            --paraguay-dark-blue: #002B7F;
            --paraguay-dark-red: #B22318;
        }

        body {
            font-family: 'Inter', sans-serif;
        }

        .bg-paraguay-red { background-color: var(--paraguay-red); }
        .bg-paraguay-blue { background-color: var(--paraguay-blue); }
        .bg-paraguay-light-blue { background-color: var(--paraguay-light-blue); }
        .bg-paraguay-light-red { background-color: var(--paraguay-light-red); }
        .text-paraguay-red { color: var(--paraguay-red); }
        .text-paraguay-blue { color: var(--paraguay-blue); }
        .border-paraguay-red { border-color: var(--paraguay-red); }
        .border-paraguay-blue { border-color: var(--paraguay-blue); }

        .btn-primary {
            background: linear-gradient(135deg, var(--paraguay-red) 0%, var(--paraguay-dark-red) 100%);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--paraguay-dark-red) 0%, var(--paraguay-red) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(213, 43, 30, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--paraguay-blue) 0%, var(--paraguay-dark-blue) 100%);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, var(--paraguay-dark-blue) 0%, var(--paraguay-blue) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 56, 168, 0.3);
        }

        .hero-gradient {
            background: linear-gradient(135deg, var(--paraguay-light-blue) 0%, var(--paraguay-light-red) 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .navbar-gradient {
            background: linear-gradient(90deg, var(--paraguay-blue) 0%, var(--paraguay-red) 100%);
        }

        /* Mobile menu styles */
        .mobile-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }

        .mobile-menu.open {
            max-height: 500px;
        }
    </style>
</head>
<body>

<nav class="navbar-gradient relative z-[999] flex w-full items-center justify-between border-b-2 border-white shadow-lg">
    <div class="container mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="w-full lg:flex lg:items-center lg:justify-between">
            <div class="flex items-center justify-between">
                <div class="flex min-h-14 items-center md:min-h-16 lg:min-h-18">
                    <a href="<?php echo $base_path; ?>inicio/index.php" class="flex items-center space-x-2 sm:space-x-3">
                        <div class="w-10 h-10 sm:w-12 sm:h-12 bg-white rounded-full flex items-center justify-center">
                            <span class="text-md sm:text-md md:text-xl font-bold text-paraguay-blue">APPI</span>
                        </div>
                        <div class="hidden sm:block">
                            <h1 class="text-white font-bold text-sm sm:text-base md:text-lg">APPI</h1>
                            <p class="text-white text-xs opacity-90">Asociación de Parques Industriales del Paraguay</p>
                        </div>
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <button id="mobile-menu-button" class="flex w-10 h-10 sm:w-12 sm:h-12 flex-col items-center justify-center lg:hidden">
                    <span class="hamburger-line my-0.5 h-0.5 w-5 sm:w-6 bg-white transition-all duration-300"></span>
                    <span class="hamburger-line my-0.5 h-0.5 w-5 sm:w-6 bg-white transition-all duration-300"></span>
                    <span class="hamburger-line my-0.5 h-0.5 w-5 sm:w-6 bg-white transition-all duration-300"></span>
                </button>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex lg:items-center lg:space-x-1">
                <a href="<?php echo $base_path; ?>inicio/index.php" 
                   class="px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'inicio') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Inicio
                </a>
                <a href="<?php echo $base_path; ?>servicios/index.php" 
                   class="px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'servicios') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Servicios
                </a>
                <a href="<?php echo $base_path; ?>nosotros/index.php" 
                   class="px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'nosotros') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Nosotros
                </a>
                <a href="<?php echo $base_path; ?>contacto/index.php" 
                   class="px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'contacto') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Contacto
                </a>
            </div>

            <!-- Desktop CTA Buttons -->
            <div class="hidden lg:flex lg:gap-4">
                <a href="<?php echo $base_path; ?>cómo-asociarse/index.php" 
                   class="btn-primary rounded-lg inline-flex gap-3 items-center justify-center whitespace-nowrap transition-all duration-300 ease-in-out px-6 py-3 font-semibold">
                   Asociarse
                </a>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="mobile-menu lg:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="<?php echo $base_path; ?>inicio/index.php" 
                   class="block px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'inicio') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Inicio
                </a>
                <a href="<?php echo $base_path; ?>servicios/index.php" 
                   class="block px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'servicios') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Servicios
                </a>
                <a href="<?php echo $base_path; ?>nosotros/index.php" 
                   class="block px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'nosotros') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Nosotros
                </a>
                <a href="<?php echo $base_path; ?>contacto/index.php" 
                   class="block px-3 py-2 text-white hover:text-yellow-200 transition-colors duration-300 <?php echo ($current_dir === 'contacto') ? 'bg-white bg-opacity-20 rounded' : ''; ?>">
                   Contacto
                </a>
                <!-- Mobile CTA Buttons -->
                <div class="mt-4 space-y-2">
                    <a href="<?php echo $base_path; ?>cómo-asociarse/index.php" 
                       class="btn-primary rounded-lg inline-flex gap-3 items-center justify-center whitespace-nowrap transition-all duration-300 ease-in-out px-5 py-2 w-full font-semibold">
                       Asociarse
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<script>
// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const hamburgerLines = document.querySelectorAll('.hamburger-line');

    mobileMenuButton.addEventListener('click', function() {
        mobileMenu.classList.toggle('open');
        
        // Animate hamburger to X
        if (mobileMenu.classList.contains('open')) {
            hamburgerLines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
            hamburgerLines[1].style.opacity = '0';
            hamburgerLines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            hamburgerLines[0].style.transform = 'none';
            hamburgerLines[1].style.opacity = '1';
            hamburgerLines[2].style.transform = 'none';
        }
    });

    // Close mobile menu when clicking on a link
    const mobileLinks = mobileMenu.querySelectorAll('a');
    mobileLinks.forEach(link => {
        link.addEventListener('click', function() {
            mobileMenu.classList.remove('open');
            hamburgerLines[0].style.transform = 'none';
            hamburgerLines[1].style.opacity = '1';
            hamburgerLines[2].style.transform = 'none';
        });
    });
});
</script>
